import { User } from "@supabase/supabase-js";
import { BaseSyntheticEvent, FC, ReactNode } from "react";
import { FieldValues, GlobalError, UseFormSetError } from "react-hook-form";
import { z } from "zod";

// nav
// NavMain.tsx
export type NavGroupItem = {
  title: string;
  path: string;
  icon?: FC;
}

export type NavGroupItems = Record<string, NavGroupItem[]>;

export interface NavMainProps {
  navItems: NavGroupItems;
}

// NavUser.tsx
export type NavUserData = Pick<User, "email" | "user_metadata">;

// form/
// AuthForm.tsx
export interface FieldConfig {
  name: string;
  label: string;
  value?: HTMLInputElement["value"];
  placeholder?: string;
  type: string;
  schema: z.ZodTypeAny;
}

export type AuthFormOnSubmit = (data: FieldValues, setError: UseFormSetError<FieldValues>, event?: BaseSyntheticEvent) => void;

export interface AuthFormProps {
  className?: string;
  formTitle: ReactNode;
  formError?: (error: GlobalError) => ReactNode;
  submitButtonContent: ReactNode;
  onSubmit: AuthFormOnSubmit;
  isLoading: boolean;
  fields: FieldConfig[];
  alternateAuth?: ReactNode;
  externalAuth?: {
    text: string;
    options: {
      icon: FC;
      name: string;
      className?: string;
      onClick?: () => void;
    }[];
  };
  children?: ReactNode;
}